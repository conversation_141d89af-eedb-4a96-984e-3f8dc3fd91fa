/**
 * MySQL 连接管理与连接池
 *
 * 高级的 MySQL 连接池管理系统，具有健康监控、自动重连和性能优化功能。
 * 提供企业级的连接处理，包括统计跟踪和优雅关闭功能。
 *
 * @fileoverview 带健康监控的 MySQL 连接池实现
 * <AUTHOR>
 * @since 1.0.0
 */
import { PoolConnection } from 'mysql2/promise';
import { DatabaseConfig } from './config.js';
/**
 * 连接池类
 *
 * 使用连接池管理 MySQL 数据库连接，具有自动健康监控、
 * 预连接预热和全面的统计跟踪功能。
 *
 * 功能特性：
 * - 自动连接池管理
 * - 可配置间隔的健康检查监控
 * - 为性能预创建最小连接数
 * - 连接统计和性能指标
 * - 带适当资源清理的优雅关闭
 * - 可配置安全设置的 SSL/TLS 支持
 *
 * @class ConnectionPool
 * @since 1.0.0
 */
export declare class ConnectionPool {
    /** 数据库配置设置 */
    private config;
    /** MySQL 连接池实例 */
    private pool;
    /** 健康检查监控间隔计时器 */
    private healthCheckInterval;
    /** 优雅终止的关闭事件标志 */
    private shutdownEvent;
    /** 连接池性能统计 */
    private connectionStats;
    /**
     * 连接池构造函数
     *
     * 使用提供的数据库配置初始化连接池。
     * 连接池在第一次连接请求时延迟创建。
     *
     * @constructor
     * @param {DatabaseConfig} config - 数据库连接配置
     */
    constructor(config: DatabaseConfig);
    /**
     * 初始化连接池
     *
     * 创建和配置带有安全设置的MySQL连接池，预热连接，
     * 并启动健康监控。此方法是幂等的，可以安全地多次调用。
     *
     * @public
     * @returns {Promise<void>} 当连接池初始化完成时解析的Promise
     * @throws {Error} 当连接池创建或初始化失败时抛出
     *
     * @example
     * await connectionPool.initialize();
     */
    initialize(): Promise<void>;
    /**
     * 预创建最小连接数
     *
     * 创建配置中指定的最小连接数，通过避免首次请求时的
     * 连接创建延迟来提高初始性能。
     *
     * @private
     * @returns {Promise<void>} 当连接预创建完成时解析的Promise
     */
    private preCreateConnections;
    /**
     * 启动健康检查监控
     *
     * 启动定期健康检查以确保连接池保持健康和响应。
     * 在启动新的健康检查间隔之前清除任何现有的健康检查间隔。
     *
     * @private
     */
    private startHealthCheck;
    /**
     * 执行健康检查
     *
     * 执行简单查询以验证连接池是否正常工作。
     * 失败会被记录但不会影响正常操作。
     *
     * @private
     * @returns {Promise<void>} 当健康检查完成时解析的Promise
     */
    private performHealthCheck;
    /**
     * 获取数据库连接
     *
     * 从连接池中检索连接，具有自动初始化和性能跟踪功能。
     * 测量连接获取时间并更新监控统计信息。
     *
     * @public
     * @returns {Promise<PoolConnection>} 解析为数据库连接的Promise
     * @throws {Error} 当连接池初始化失败或无法获取连接时抛出
     *
     * @example
     * const connection = await pool.getConnection();
     * try {
     *   const [rows] = await connection.execute('SELECT * FROM users');
     *   return rows;
     * } finally {
     *   connection.release();
     * }
     */
    getConnection(): Promise<PoolConnection>;
    /**
     * 获取连接池统计信息
     *
     * 返回关于连接池的综合统计信息，包括配置、性能指标
     * 和健康状态，用于监控和调试目的。
     *
     * @public
     * @returns {Record<string, any>} 连接池统计信息和配置信息
     *
     * @example
     * const stats = pool.getStats();
     * console.log(`连接池命中: ${stats.connection_stats.pool_hits}`);
     */
    getStats(): Record<string, any>;
    /**
     * 关闭连接池
     *
     * 执行连接池的优雅关闭，包括停止健康检查、
     * 关闭所有连接和清理资源。应在应用程序关闭时调用。
     *
     * @public
     * @returns {Promise<void>} 当连接池完全关闭时解析的Promise
     *
     * @example
     * // 优雅关闭
     * await pool.close();
     */
    close(): Promise<void>;
}
//# sourceMappingURL=connection.d.ts.map