{"version": 3, "file": "rateLimit.js", "sourceRoot": "", "sources": ["../src/rateLimit.ts"], "names": [], "mappings": "AAAA;;;;;;;;;GASG;AAEH;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,OAAO,sBAAsB;IACjC,kBAAkB;IACV,QAAQ,CAAS;IAEzB,eAAe;IACP,MAAM,CAAS;IAEvB,qBAAqB;IACb,UAAU,CAAS;IAE3B,mBAAmB;IACX,MAAM,CAAS;IAEvB,qBAAqB;IACb,UAAU,CAAS;IAE3B;;;;;;;;;;;;;;OAcG;IACH,YAAY,QAAgB,EAAE,UAAkB,EAAE,SAAiB,EAAE;QACnE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,UAAU;QAClC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACI,YAAY,CAAC,kBAA0B,CAAC;QAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,mBAAmB;QACnB,MAAM,OAAO,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO;QACvD,MAAM,WAAW,GAAG,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC;QAE9C,YAAY;QACZ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QAEtB,eAAe;QACf,IAAI,IAAI,CAAC,MAAM,IAAI,eAAe,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,IAAI,eAAe,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,OAAO,mBAAmB;IAC9B,qBAAqB;IACb,SAAS,CAAS;IAE1B,mBAAmB;IACX,MAAM,CAAS;IAEvB,0BAA0B;IAClB,gBAAgB,CAAS;IAEjC,qBAAqB;IACb,OAAO,CAAsC;IAErD;;;;;;;;;;;;;OAaG;IACH,YAAY,SAAiB,EAAE,SAAiB,EAAE;QAChD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,SAAS;QACtC,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAkC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACI,gBAAgB,CAAC,QAAgB,EAAE,WAAmB;QAC3D,IAAI,QAAQ,GAAG,GAAG,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;YACxC,oBAAoB;YACpB,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC9B,CAAC;aAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;YAC/C,wBAAwB;YACxB,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,kBAAkB;YAClB,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACI,cAAc,CAAC,UAAkB;QACtC,mBAAmB;QACnB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEzE,eAAe;QACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,MAAM,UAAU,GAAG,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,sBAAsB,CACrD,aAAa,EACb,UAAU,EACV,IAAI,CAAC,MAAM,CACZ,CAAC,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,YAAY,EAAE,CAAC;IACtD,CAAC;CACF"}