/**
 * MySQL MCP 服务器常量
 *
 * 包含 MySQL 错误代码、默认配置、字符串常量和系统参数的综合常量集合。
 * 提供集中的配置管理和错误处理。
 *
 * @fileoverview 中央常量和配置定义
 * <AUTHOR>
 * @since 1.0.0
 */
/**
 * MySQL 错误代码
 *
 * 按类别组织的标准 MySQL 错误代码，用于正确的错误处理和用户友好的错误消息。
 * 基于官方 MySQL 文档。
 *
 * 类别：
 * - 访问控制：身份验证和授权错误
 * - 对象解析：数据库对象未找到错误
 * - 数据完整性：约束违反和重复键错误
 * - SQL 语法：查询解析和语法错误
 * - 连接：网络和连接相关错误
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // 检查特定错误类型
 * if (error.code === MySQLErrorCodes.ACCESS_DENIED) {
 *   throw new Error('无效凭据');
 * }
 */
export declare const MySQLErrorCodes: {
    /** 访问控制错误 - 身份验证和授权失败 */
    readonly ACCESS_DENIED: 1045;
    readonly ACCESS_DENIED_FOR_USER: 1044;
    readonly TABLE_ACCESS_DENIED: 1142;
    readonly COLUMN_ACCESS_DENIED: 1143;
    /** 对象解析错误 - 数据库对象未找到 */
    readonly UNKNOWN_DATABASE: 1049;
    readonly TABLE_DOESNT_EXIST: 1146;
    readonly UNKNOWN_COLUMN: 1054;
    readonly UNKNOWN_TABLE: 1109;
    /** 数据完整性错误 - 约束违反和重复 */
    readonly DUPLICATE_ENTRY: 1062;
    readonly DUPLICATE_ENTRY_WITH_KEY_NAME: 1586;
    readonly DUPLICATE_KEY_NAME: 1557;
    /** SQL 语法错误 - 查询解析和语法问题 */
    readonly PARSE_ERROR: 1064;
    readonly SYNTAX_ERROR: 1149;
    readonly PARSE_ERROR_NEAR: 1065;
    /** 连接错误 - 网络和连接问题 */
    readonly CANT_CONNECT_TO_SERVER: 2003;
    readonly LOST_CONNECTION: 2013;
    readonly SERVER_HAS_GONE_AWAY: 2006;
};
/**
 * 默认配置
 *
 * 所有系统组件的综合默认配置值。提供安全、性能优化的默认值，
 * 可通过环境变量覆盖。
 *
 * 配置类别：
 * - MySQL 连接：数据库连接和连接池设置
 * - 安全：访问控制和验证限制
 * - 缓存：缓存大小和过期设置
 * - 性能：监控和优化参数
 * - 可靠性：重试和错误处理配置
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // 使用环境变量覆盖的默认值
 * const port = process.env.MYSQL_PORT || DefaultConfig.MYSQL_PORT;
 */
export declare const DefaultConfig: {
    /** MySQL 连接配置 - 数据库连接设置 */
    readonly MYSQL_PORT: 3306;
    readonly CONNECTION_LIMIT: 10;
    readonly MIN_CONNECTIONS: 2;
    readonly CONNECT_TIMEOUT: 60;
    readonly IDLE_TIMEOUT: 60;
    /** 安全配置 - 访问控制和验证限制 */
    readonly MAX_QUERY_LENGTH: 10000;
    readonly MAX_RESULT_ROWS: 1000;
    readonly QUERY_TIMEOUT: 30;
    readonly RATE_LIMIT_WINDOW: 60;
    readonly RATE_LIMIT_MAX: 100;
    /** 输入验证配置 - 数据验证限制 */
    readonly MAX_INPUT_LENGTH: 1000;
    readonly MAX_TABLE_NAME_LENGTH: 64;
    /** 缓存配置 - 缓存系统参数 */
    readonly SCHEMA_CACHE_SIZE: 128;
    readonly TABLE_EXISTS_CACHE_SIZE: 64;
    readonly INDEX_CACHE_SIZE: 64;
    readonly CACHE_TTL: 300;
    /** 连接池配置 - 连接池管理设置 */
    readonly HEALTH_CHECK_INTERVAL: 30;
    readonly CONNECTION_MAX_AGE: 3600;
    /** 性能监控配置 - 指标和监控 */
    readonly METRICS_WINDOW_SIZE: 1000;
    readonly SLOW_QUERY_THRESHOLD: 1;
    /** 重试配置 - 错误处理和恢复 */
    readonly RECONNECT_ATTEMPTS: 3;
    readonly RECONNECT_DELAY: 1;
    readonly MAX_RETRY_ATTEMPTS: 3;
    /** 批处理配置 - 批量操作设置 */
    readonly BATCH_SIZE: 1000;
    /** 日志配置 - 日志输出和格式化 */
    readonly MAX_LOG_DETAIL_LENGTH: 100;
};
/**
 * 字符串常量
 *
 * 用于一致消息传递、配置键和系统标识符的集中字符串常量。
 * 按功能类别组织，便于维护和本地化支持。
 *
 * 类别：
 * - 数据库配置：连接和连接池设置
 * - 环境变量：配置覆盖键
 * - SQL 操作：查询类型和 SQL 相关常量
 * - 错误消息：用户友好的错误消息模板
 * - 系统状态：状态代码和状态指示器
 * - 字段名称：JSON 响应字段标识符
 *
 * @constant
 * @since 1.0.0
 *
 * @example
 * // 使用字符串常量保持一致性
 * const host = process.env[StringConstants.ENV_MYSQL_HOST] || StringConstants.DEFAULT_HOST;
 */
export declare const StringConstants: {
    /** 数据库配置字符串 - 默认连接设置 */
    readonly DEFAULT_HOST: "localhost";
    readonly DEFAULT_USER: "root";
    readonly DEFAULT_PASSWORD: "";
    readonly DEFAULT_DATABASE: "";
    readonly POOL_NAME: "mysql_mcp_pool";
    readonly CHARSET: "utf8mb4";
    readonly SQL_MODE: "TRADITIONAL";
    /** 环境变量键 */
    readonly ENV_MYSQL_HOST: "MYSQL_HOST";
    readonly ENV_MYSQL_PORT: "MYSQL_PORT";
    readonly ENV_MYSQL_USER: "MYSQL_USER";
    readonly ENV_MYSQL_PASSWORD: "MYSQL_PASSWORD";
    readonly ENV_MYSQL_DATABASE: "MYSQL_DATABASE";
    readonly ENV_MYSQL_SSL: "MYSQL_SSL";
    readonly ENV_CONNECTION_LIMIT: "MYSQL_CONNECTION_LIMIT";
    readonly ENV_CONNECT_TIMEOUT: "MYSQL_CONNECT_TIMEOUT";
    readonly ENV_IDLE_TIMEOUT: "MYSQL_IDLE_TIMEOUT";
    readonly ENV_ALLOWED_QUERY_TYPES: "ALLOWED_QUERY_TYPES";
    readonly ENV_MAX_QUERY_LENGTH: "MAX_QUERY_LENGTH";
    readonly ENV_MAX_RESULT_ROWS: "MAX_RESULT_ROWS";
    readonly ENV_QUERY_TIMEOUT: "QUERY_TIMEOUT";
    readonly ENV_RATE_LIMIT_WINDOW: "RATE_LIMIT_WINDOW";
    readonly ENV_RATE_LIMIT_MAX: "RATE_LIMIT_MAX";
    /** SQL 查询类型 */
    readonly SQL_SELECT: "SELECT";
    readonly SQL_SHOW: "SHOW";
    readonly SQL_DESCRIBE: "DESCRIBE";
    readonly SQL_INSERT: "INSERT";
    readonly SQL_UPDATE: "UPDATE";
    readonly SQL_DELETE: "DELETE";
    readonly SQL_CREATE: "CREATE";
    readonly SQL_DROP: "DROP";
    readonly SQL_ALTER: "ALTER";
    /** 默认允许的查询类型 */
    readonly DEFAULT_ALLOWED_QUERY_TYPES: "SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER";
    /** 危险的 SQL 模式 */
    readonly DANGEROUS_PATTERNS: readonly ["--", "/*", "*/", "xp_", "sp_"];
    /** 错误类别 */
    readonly ERROR_CATEGORY_ACCESS_DENIED: "access_denied";
    readonly ERROR_CATEGORY_OBJECT_NOT_FOUND: "object_not_found";
    readonly ERROR_CATEGORY_CONSTRAINT_VIOLATION: "constraint_violation";
    readonly ERROR_CATEGORY_SYNTAX_ERROR: "syntax_error";
    readonly ERROR_CATEGORY_CONNECTION_ERROR: "connection_error";
    readonly ERROR_CATEGORY_UNKNOWN: "unknown";
    /** 错误严重级别 */
    readonly SEVERITY_HIGH: "high";
    readonly SEVERITY_MEDIUM: "medium";
    readonly SEVERITY_LOW: "low";
    /** 日志事件类型 */
    readonly LOG_EVENT_SECURITY: "[SECURITY]";
    /** 错误消息模板 */
    readonly MSG_DATABASE_ACCESS_DENIED: "数据库访问被拒绝，请检查用户名和密码";
    readonly MSG_DATABASE_OBJECT_NOT_FOUND: "未找到数据库对象";
    readonly MSG_DATABASE_CONSTRAINT_VIOLATION: "数据约束违反";
    readonly MSG_DATABASE_SYNTAX_ERROR: "SQL语法错误";
    readonly MSG_DATABASE_CONNECTION_ERROR: "数据库连接错误";
    readonly MSG_MYSQL_CONNECTION_POOL_FAILED: "MySQL连接池创建失败";
    readonly MSG_MYSQL_CONNECTION_ERROR: "MySQL连接错误";
    readonly MSG_MYSQL_QUERY_ERROR: "MySQL查询错误";
    readonly MSG_RATE_LIMIT_EXCEEDED: "超出速率限制。请稍后再试。";
    readonly MSG_QUERY_TOO_LONG: "查询超过最大允许长度";
    readonly MSG_PROHIBITED_OPERATIONS: "查询包含禁止的操作";
    readonly MSG_QUERY_TYPE_NOT_ALLOWED: "不允许查询类型 '{query_type}'";
    readonly MSG_INVALID_TABLE_NAME: "无效的表名格式";
    readonly MSG_TABLE_NAME_TOO_LONG: "表名超过最大长度";
    readonly MSG_INVALID_CHARACTER: "{field_name} 中包含无效字符";
    readonly MSG_INPUT_TOO_LONG: "{field_name} 超过最大长度";
    readonly MSG_DANGEROUS_CONTENT: "{field_name} 中包含潜在危险内容";
    /** 状态字符串 */
    readonly STATUS_SUCCESS: "success";
    readonly STATUS_FAILED: "failed";
    readonly STATUS_NOT_INITIALIZED: "not_initialized";
    readonly STATUS_ERROR: "error";
    readonly STATUS_KEY: "status";
    readonly ERROR_KEY: "error";
    /** 特殊值 */
    readonly NULL_BYTE: "\0";
    readonly TRUE_STRING: "true";
    /** 服务器相关常量 */
    readonly SERVER_NAME: "mysql-mcp-server";
    /** 错误消息 */
    readonly MSG_FASTMCP_NOT_INSTALLED: "错误：FastMCP未安装。请使用以下命令安装：npm install fastmcp";
    readonly MSG_ERROR_DURING_CLEANUP: "清理过程中出错：";
    readonly MSG_SIGNAL_RECEIVED: "收到信号";
    readonly MSG_GRACEFUL_SHUTDOWN: "正在优雅关闭...";
    readonly MSG_SERVER_RUNNING: "MySQL MCP 服务器 (FastMCP) 在 stdio 上运行";
    readonly MSG_SERVER_ERROR: "服务器错误：";
    /** 工具函数错误消息 */
    readonly MSG_QUERY_FAILED: "查询失败：";
    readonly MSG_SHOW_TABLES_FAILED: "显示表失败：";
    readonly MSG_DESCRIBE_TABLE_FAILED: "描述表失败：";
    readonly MSG_GET_METRICS_FAILED: "获取性能指标失败：";
    readonly MSG_SELECT_DATA_FAILED: "选择数据失败：";
    readonly MSG_INSERT_DATA_FAILED: "插入数据失败：";
    readonly MSG_UPDATE_DATA_FAILED: "更新数据失败：";
    readonly MSG_DELETE_DATA_FAILED: "删除数据失败：";
    readonly MSG_GET_SCHEMA_FAILED: "获取模式失败：";
    readonly MSG_GET_INDEXES_FAILED: "获取索引失败：";
    readonly MSG_GET_FOREIGN_KEYS_FAILED: "获取外键失败：";
    readonly MSG_CREATE_TABLE_FAILED: "创建表失败：";
    readonly MSG_DROP_TABLE_FAILED: "删除表失败：";
    readonly MSG_DIAGNOSE_FAILED: "诊断失败：";
    readonly MSG_GET_POOL_STATUS_FAILED: "获取连接池状态失败：";
    /** 连接池相关常量 */
    readonly MSG_FAILED_TO_INIT_POOL: "初始化连接池失败：";
    /** JSON 响应字段常量 */
    readonly SUCCESS_KEY: "success";
    /** SQL 关键字常量 */
    readonly SQL_IF_EXISTS: "IF EXISTS";
    /** 性能指标字段常量 */
    readonly FIELD_QUERY_COUNT: "query_count";
    readonly FIELD_AVG_QUERY_TIME: "avg_query_time";
    readonly FIELD_SLOW_QUERY_COUNT: "slow_query_count";
    readonly FIELD_ERROR_COUNT: "error_count";
    readonly FIELD_ERROR_RATE: "error_rate";
    readonly FIELD_CACHE_HIT_RATE: "cache_hit_rate";
    readonly FIELD_CONNECTION_POOL_HITS: "connection_pool_hits";
    readonly FIELD_CONNECTION_POOL_WAITS: "connection_pool_waits";
    /** 缓存统计字段常量 */
    readonly FIELD_SIZE: "size";
    readonly FIELD_MAX_SIZE: "max_size";
    readonly FIELD_HIT_COUNT: "hit_count";
    readonly FIELD_MISS_COUNT: "miss_count";
    readonly FIELD_HIT_RATE: "hit_rate";
    readonly FIELD_TTL: "ttl";
    /** 连接池统计字段常量 */
    readonly FIELD_POOL_NAME: "pool_name";
    readonly FIELD_POOL_SIZE: "pool_size";
    readonly FIELD_AVAILABLE_CONNECTIONS: "available_connections";
    readonly FIELD_CONNECTION_STATS: "connection_stats";
    readonly FIELD_HEALTH_CHECK_ACTIVE: "health_check_active";
    readonly FIELD_POOL_HITS: "pool_hits";
    readonly FIELD_POOL_WAITS: "pool_waits";
    /** 性能报告部分常量 */
    readonly SECTION_PERFORMANCE: "performance";
    readonly SECTION_CACHE_STATS: "cache_stats";
    readonly SECTION_CONNECTION_POOL: "connection_pool";
    readonly SECTION_SCHEMA_CACHE: "schema_cache";
    readonly SECTION_TABLE_EXISTS_CACHE: "table_exists_cache";
    readonly SECTION_INDEX_CACHE: "index_cache";
    /** 配置字段常量 */
    readonly FIELD_HOST: "host";
    readonly FIELD_PORT: "port";
    readonly FIELD_DATABASE: "database";
    readonly FIELD_CONNECTION_LIMIT: "connection_limit";
    readonly FIELD_CONNECT_TIMEOUT: "connect_timeout";
    readonly FIELD_SSL_ENABLED: "ssl_enabled";
    /** 诊断报告字段常量 */
    readonly FIELD_CONNECTION_POOL_STATUS: "connection_pool_status";
    readonly FIELD_CONFIG: "config";
    readonly FIELD_SECURITY_CONFIG: "security_config";
    readonly FIELD_PERFORMANCE_METRICS: "performance_metrics";
    readonly FIELD_CONNECTION_TEST: "connection_test";
    readonly FIELD_MAX_QUERY_LENGTH: "max_query_length";
    readonly FIELD_MAX_RESULT_ROWS: "max_result_rows";
    readonly FIELD_RATE_LIMIT_MAX: "rate_limit_max";
    readonly FIELD_ALLOWED_QUERY_TYPES: "allowed_query_types";
    readonly FIELD_RESULT: "result";
};
//# sourceMappingURL=constants.d.ts.map