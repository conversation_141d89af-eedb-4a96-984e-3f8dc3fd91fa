/**
 * MySQL 管理器 - 主集成类
 *
 * 综合的 MySQL 管理系统，集成了连接池、缓存、安全验证、
 * 速率限制和性能监控功能。为所有数据库操作提供统一接口，
 * 具备企业级的可靠性和安全特性。
 *
 * @fileoverview 集成组件的核心 MySQL 管理类
 * <AUTHOR>
 * @since 1.0.0
 */
/**
 * MySQL 管理器类
 *
 * 中央管理类，协调所有 MySQL 操作，集成了安全性、
 * 性能监控、缓存和连接管理功能。
 *
 * 功能特性：
 * - 带健康监控的连接池
 * - 多级缓存（模式、表存在性、索引）
 * - 安全验证和 SQL 注入防护
 * - 基于系统负载的自适应速率限制
 * - 全面的性能指标和告警
 * - 指数退避的自动重试
 * - 优雅的错误处理和恢复
 *
 * @class MySQLManager
 * @since 1.0.0
 */
export declare class MySQLManager {
    /** 用于跟踪和调试的唯一会话标识符 */
    private sessionId;
    /** 数据库、安全和缓存设置的配置管理器 */
    private configManager;
    /** 高效数据库连接的连接池管理器 */
    private connectionPool;
    /** 表模式信息的智能缓存 */
    private schemaCache;
    /** 表存在性检查的智能缓存 */
    private tableExistsCache;
    /** 索引信息的智能缓存 */
    private indexCache;
    /** 传统性能指标收集器 */
    private metrics;
    /** 带时间序列数据和告警的增强指标管理器 */
    private enhancedMetrics;
    /** 用于输入清理和 SQL 注入防护的安全验证器 */
    private securityValidator;
    /** 请求节流的自适应速率限制器 */
    private adaptiveRateLimiter;
    /** 处理瞬态错误的重试策略 */
    private retryStrategy;
    /**
     * 用于安全验证的预编译危险SQL模式
     * 这些模式检测可能危害系统安全或数据完整性的潜在有害SQL操作。
     */
    private static DANGEROUS_PATTERNS;
    /**
     * 表名验证模式
     * 确保表名只包含安全字符（字母数字、下划线、连字符）
     */
    private static TABLE_NAME_PATTERN;
    /**
     * MySQL 管理器构造函数
     *
     * 初始化MySQL管理系统的所有组件，包括配置、连接池、
     * 缓存、安全和监控功能。
     *
     * @constructor
     * @throws {Error} 当组件初始化失败时抛出
     */
    constructor();
    /**
     * 使用重试机制执行操作
     *
     * 使用指数退避自动重试执行数据库操作。
     * 处理瞬态错误，同时避免对永久性故障进行重试。
     *
     * @private
     * @template T - 操作的返回类型
     * @param operation - 要使用重试逻辑执行的异步操作
     * @returns 解析为操作结果的Promise
     * @throws {Error} 当所有重试尝试都用尽或发生不可重试错误时抛出
     */
    private executeWithRetry;
    /**
     * 验证输入数据
     *
     * 对输入数据执行综合安全验证，以防止SQL注入和其他安全漏洞。
     *
     * @private
     * @param inputValue - 要验证的值（字符串、数字、布尔值、null、undefined）
     * @param fieldName - 被验证字段的名称（用于错误消息）
     * @param validationLevel - 验证严格级别（"strict"、"moderate"、"basic"）
     * @throws {Error} 当输入未通过安全验证时抛出
     */
    private validateInput;
    /**
     * 验证SQL查询安全性
     *
     * 对SQL查询执行综合安全验证，包括长度限制、
     * 危险模式检测和查询类型限制。
     *
     * @private
     * @param query - 要验证的SQL查询字符串
     * @throws {Error} 当查询未通过安全验证时抛出
     */
    private validateQuery;
    /**
     * 验证表名
     *
     * 根据安全模式和长度限制验证表名，
     * 以防止SQL注入并确保兼容性。
     *
     * @private
     * @param tableName - 要验证的表名
     * @throws {Error} 当表名无效或过长时抛出
     */
    private validateTableName;
    /**
     * 检查速率限制
     *
     * 使用自适应速率限制器验证当前请求是否在速率限制范围内。
     *
     * @private
     * @param identifier - 速率限制的唯一标识符（默认为"default"）
     * @throws {Error} 当超出速率限制时抛出
     */
    private checkRateLimit;
    /**
     * 使用缓存获取表模式
     *
     * 使用智能缓存检索表模式信息以提高性能。
     * 缓存未命中触发数据库查询，而命中立即返回缓存数据。
     *
     * @private
     * @param tableName - 要获取模式的表名
     * @returns 解析为表模式信息的Promise
     * @throws {Error} 当模式查询失败时抛出
     */
    private getTableSchemaCached;
    /**
     * 使用缓存检查表存在性
     *
     * 使用缓存验证表是否存在于当前数据库中，
     * 以避免重复的INFORMATION_SCHEMA查询。
     *
     * @private
     * @param tableName - 要检查的表名
     * @returns 如果表存在则解析为true，否则为false的Promise
     * @throws {Error} 当存在性检查查询失败时抛出
     */
    private tableExistsCached;
    /**
     * 执行SQL查询
     *
     * 执行SQL查询的主要公共方法，具有综合安全性、性能监控、
     * 缓存和错误处理功能。包括速率限制、重试机制和指标收集。
     *
     * @public
     * @param query - 要执行的SQL查询字符串
     * @param params - 预处理语句的可选参数
     * @returns 解析为查询结果的Promise
     * @throws {Error} 当超出速率限制、安全验证失败或查询执行失败时抛出
     *
     * @example
     * // 简单查询
     * const results = await manager.executeQuery("SELECT * FROM users LIMIT 10");
     *
     * @example
     * // 参数化查询
     * const user = await manager.executeQuery("SELECT * FROM users WHERE id = ?", [123]);
     */
    executeQuery(query: string, params?: any[]): Promise<any>;
    /**
     * 内部查询执行
     *
     * 处理实际数据库连接和查询执行的低级方法。
     * 管理连接生命周期并确保适当的资源清理。
     *
     * @private
     * @param query - SQL查询字符串
     * @param params - 可选查询参数
     * @returns 解析为原始查询结果的Promise
     * @throws {Error} 当连接或查询执行失败时抛出
     */
    private executeQueryInternal;
    /**
     * 更新性能指标
     *
     * 更新查询执行的性能指标，包括时间、错误和慢查询统计。
     *
     * @private
     * @param queryTime - 查询执行时间（秒）
     * @param isError - 是否发生错误
     * @param isSlow - 是否为慢查询
     */
    private updateMetrics;
    /**
     * 使缓存失效
     *
     * 根据操作类型使相关缓存失效。DDL操作清除所有缓存，
     * DML操作清除特定表的缓存。
     *
     * @public
     * @param operationType - 操作类型（DDL、DML等）
     * @param tableName - 可选的表名，用于特定表缓存失效
     */
    invalidateCaches(operationType?: string, tableName?: string): void;
    /**
     * 清除所有缓存
     *
     * @private
     */
    private clearAllCaches;
    /**
     * 使特定表缓存失效
     *
     * @private
     * @param tableName - 要使缓存失效的表名
     */
    private invalidateTableSpecificCache;
    /**
     * 获取性能指标
     *
     * 检索综合性能指标，包括查询统计、缓存性能和
     * 连接池状态，用于监控和调试。
     *
     * @public
     * @returns 包含详细性能指标的对象
     *
     * @example
     * const metrics = manager.getPerformanceMetrics();
     * console.log(`缓存命中率: ${metrics.cache_stats.schema_cache.hit_rate}`);
     */
    getPerformanceMetrics(): Record<string, any>;
    /**
     * 关闭MySQL管理器
     *
     * 执行所有组件的优雅关闭，包括指标监控、连接池关闭和缓存清理。
     * 应在应用程序关闭期间调用以防止资源泄漏。
     *
     * @public
     * @returns 当所有清理完成时解析的Promise
     *
     * @example
     * // 优雅关闭
     * await manager.close();
     */
    close(): Promise<void>;
}
//# sourceMappingURL=mysqlManager.d.ts.map