/**
 * MySQL MCP 服务器实现
 *
 * 完整的 MySQL 数据库操作模型上下文协议 (MCP) 服务器。
 * 提供安全、高性能的 CRUD 操作、模式管理和连接诊断工具，
 * 内置缓存、速率限制和安全验证功能。
 *
 * @fileoverview MySQL MCP 服务器的主入口点
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
import { FastMCP } from 'fastmcp';
import { MySQLManager } from './mysqlManager.js';
/**
 * 全局 MySQL 连接管理器实例
 * 处理所有数据库操作，包括连接池、缓存和安全验证
 */
declare const mysqlManager: MySQLManager;
/**
 * FastMCP 服务器实例配置
 * 使用常量中的服务器名称和版本进行配置
 */
declare const mcp: FastMCP<undefined>;
/**
 * 模块导出
 *
 * 导出配置的MCP服务器实例和MySQL管理器，
 * 用于外部使用、测试或与其他模块集成。
 */
export { mcp, mysqlManager };
/**
 * 服务器启动函数
 *
 * 初始化并启动MySQL MCP服务器，包含错误处理。
 * 确保在启动失败时进行适当的清理。
 *
 * @async
 * @function startServer
 * @returns {Promise<void>} 当服务器成功启动时解析的Promise
 * @throws {Error} 当服务器初始化失败时抛出
 */
export declare function startServer(): Promise<void>;
//# sourceMappingURL=index.d.ts.map