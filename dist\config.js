/**
 * 配置管理系统
 *
 * MySQL MCP 服务器的综合配置管理，包括数据库连接设置、
 * 安全策略和缓存参数。支持环境变量覆盖并提供安全的默认值。
 *
 * @fileoverview 配置接口和管理类
 * <AUTHOR>
 * @since 1.0.0
 */
import { DefaultConfig, StringConstants } from './constants.js';
/**
 * 配置管理器类
 *
 * 中央配置管理类，从环境变量加载和验证所有配置设置，
 * 并提供安全的默认值。提供对数据库、安全和缓存配置的类型安全访问。
 *
 * @class ConfigurationManager
 * @since 1.0.0
 */
export class ConfigurationManager {
    /** 数据库连接和连接池配置 */
    database;
    /** 安全策略和访问控制配置 */
    security;
    /** 缓存系统配置 */
    cache;
    /**
     * 配置管理器构造函数
     *
     * 通过从环境变量加载来初始化所有配置部分，
     * 并回退到安全的默认值。
     *
     * @constructor
     */
    constructor() {
        this.database = this.loadDatabaseConfig();
        this.security = this.loadSecurityConfig();
        this.cache = this.loadCacheConfig();
    }
    /**
     * 加载数据库配置
     *
     * 从环境变量加载数据库连接设置，包含验证和安全默认值。
     * 支持MySQL连接参数，包括SSL、超时和连接池。
     *
     * @private
     * @returns {DatabaseConfig} 已验证的数据库配置对象
     */
    loadDatabaseConfig() {
        return {
            host: process.env[StringConstants.ENV_MYSQL_HOST] || StringConstants.DEFAULT_HOST,
            port: parseInt(process.env[StringConstants.ENV_MYSQL_PORT] || DefaultConfig.MYSQL_PORT.toString(), 10),
            user: process.env[StringConstants.ENV_MYSQL_USER] || StringConstants.DEFAULT_USER,
            password: process.env[StringConstants.ENV_MYSQL_PASSWORD] || StringConstants.DEFAULT_PASSWORD,
            database: process.env[StringConstants.ENV_MYSQL_DATABASE] || StringConstants.DEFAULT_DATABASE,
            connectionLimit: parseInt(process.env[StringConstants.ENV_CONNECTION_LIMIT] || DefaultConfig.CONNECTION_LIMIT.toString(), 10),
            minConnections: DefaultConfig.MIN_CONNECTIONS,
            connectTimeout: parseInt(process.env[StringConstants.ENV_CONNECT_TIMEOUT] || DefaultConfig.CONNECT_TIMEOUT.toString(), 10),
            idleTimeout: parseInt(process.env[StringConstants.ENV_IDLE_TIMEOUT] || DefaultConfig.IDLE_TIMEOUT.toString(), 10),
            sslEnabled: (process.env[StringConstants.ENV_MYSQL_SSL] || '').toLowerCase() === StringConstants.TRUE_STRING
        };
    }
    /**
     * 加载安全配置
     *
     * 从环境变量加载安全策略，包括查询限制、
     * 速率限制和访问控制设置。
     *
     * @private
     * @returns {SecurityConfig} 已验证的安全配置对象
     */
    loadSecurityConfig() {
        const allowedTypesStr = process.env[StringConstants.ENV_ALLOWED_QUERY_TYPES] || StringConstants.DEFAULT_ALLOWED_QUERY_TYPES;
        const allowedTypes = allowedTypesStr.split(',').map(t => t.trim().toUpperCase());
        return {
            maxQueryLength: parseInt(process.env[StringConstants.ENV_MAX_QUERY_LENGTH] || DefaultConfig.MAX_QUERY_LENGTH.toString(), 10),
            allowedQueryTypes: allowedTypes,
            maxResultRows: parseInt(process.env[StringConstants.ENV_MAX_RESULT_ROWS] || DefaultConfig.MAX_RESULT_ROWS.toString(), 10),
            queryTimeout: parseInt(process.env[StringConstants.ENV_QUERY_TIMEOUT] || DefaultConfig.QUERY_TIMEOUT.toString(), 10),
            rateLimitMax: parseInt(process.env[StringConstants.ENV_RATE_LIMIT_MAX] || DefaultConfig.RATE_LIMIT_MAX.toString(), 10),
            rateLimitWindow: parseInt(process.env[StringConstants.ENV_RATE_LIMIT_WINDOW] || DefaultConfig.RATE_LIMIT_WINDOW.toString(), 10)
        };
    }
    /**
     * 加载缓存配置
     *
     * 从环境变量加载缓存系统参数，包括缓存大小和
     * 生存时间设置以获得最佳性能。
     *
     * @private
     * @returns {CacheConfig} 已验证的缓存配置对象
     */
    loadCacheConfig() {
        return {
            schemaCacheSize: parseInt(process.env.SCHEMA_CACHE_SIZE || DefaultConfig.SCHEMA_CACHE_SIZE.toString(), 10),
            tableExistsCacheSize: parseInt(process.env.TABLE_EXISTS_CACHE_SIZE || DefaultConfig.TABLE_EXISTS_CACHE_SIZE.toString(), 10),
            indexCacheSize: parseInt(process.env.INDEX_CACHE_SIZE || DefaultConfig.INDEX_CACHE_SIZE.toString(), 10),
            cacheTTL: parseInt(process.env.CACHE_TTL || DefaultConfig.CACHE_TTL.toString(), 10)
        };
    }
    /**
     * 导出配置用于诊断
     *
     * 返回适用于诊断和日志记录的清理配置对象。
     * 敏感信息如密码会被掩码以确保安全。
     *
     * @public
     * @returns {Record<string, any>} 清理后的配置对象
     *
     * @example
     * const config = manager.toObject();
     * console.log(JSON.stringify(config, null, 2));
     */
    toObject() {
        const configObj = {
            database: { ...this.database },
            security: { ...this.security },
            cache: { ...this.cache }
        };
        // 为安全起见掩码敏感信息
        configObj.database.password = '***';
        return configObj;
    }
    /**
     * 获取配置摘要
     *
     * 返回关键配置参数的简洁摘要字符串，
     * 用于快速状态检查和监控仪表板。
     *
     * @public
     * @returns {Record<string, string>} 关键配置参数的字符串形式
     *
     * @example
     * const summary = manager.getSummary();
     * console.log(`数据库: ${summary.database_host}:${summary.database_port}`);
     */
    getSummary() {
        return {
            database_host: this.database.host,
            database_port: this.database.port.toString(),
            connection_limit: this.database.connectionLimit.toString(),
            max_result_rows: this.security.maxResultRows.toString(),
            rate_limit_max: this.security.rateLimitMax.toString(),
            schema_cache_size: this.cache.schemaCacheSize.toString()
        };
    }
}
//# sourceMappingURL=config.js.map